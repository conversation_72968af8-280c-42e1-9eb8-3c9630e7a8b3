["tests/test_config.py::TestConfigManager::test_config_manager_initialization", "tests/test_config.py::TestConfigManager::test_get_available_extractions", "tests/test_config.py::TestConfigManager::test_get_extraction_config", "tests/test_config.py::TestConfigManager::test_invalid_json_schema", "tests/test_config.py::TestConfigManager::test_load_prompt", "tests/test_config.py::TestConfigManager::test_load_queries", "tests/test_config.py::TestConfigManager::test_load_schema", "tests/test_config.py::TestConfigManager::test_missing_schema_file", "tests/test_config.py::TestConfigManager::test_reload_config", "tests/test_config.py::TestConfigManager::test_validate_extraction_config", "tests/test_config.py::TestSettings::test_get_available_models", "tests/test_config.py::TestSettings::test_get_model_provider", "tests/test_config.py::TestSettings::test_settings_from_environment", "tests/test_config.py::TestSettings::test_settings_initialization", "tests/test_config.py::TestSettings::test_validate_configuration", "tests/test_extraction.py::TestExtractionModels::test_model_validation", "tests/test_extraction.py::TestExtractionModels::test_spare_part_model", "tests/test_extraction.py::TestExtractionModels::test_spare_parts_list_model", "tests/test_extraction.py::TestLLMExtractor::test_estimate_tokens", "tests/test_extraction.py::TestLLMExtractor::test_extract_empty_chunks", "tests/test_extraction.py::TestLLMExtractor::test_extract_success", "tests/test_extraction.py::TestLLMExtractor::test_extract_with_fallback", "tests/test_extraction.py::TestLLMExtractor::test_extractor_initialization_anthropic", "tests/test_extraction.py::TestLLMExtractor::test_extractor_initialization_openai", "tests/test_extraction.py::TestLLMExtractor::test_prepare_context", "tests/test_extraction.py::TestLLMExtractor::test_validate_and_extract", "tests/test_retrieval.py::TestChunkProcessor::test_aggregate_chunks", "tests/test_retrieval.py::TestChunkProcessor::test_calculate_text_similarity", "tests/test_retrieval.py::TestChunkProcessor::test_deduplicate_chunks", "tests/test_retrieval.py::TestChunkProcessor::test_filter_chunks_by_file_names", "tests/test_retrieval.py::TestChunkProcessor::test_filter_chunks_by_keywords", "tests/test_retrieval.py::TestChunkProcessor::test_filter_chunks_by_page_range", "tests/test_retrieval.py::TestChunkProcessor::test_filter_chunks_by_similarity", "tests/test_retrieval.py::TestChunkProcessor::test_get_chunk_statistics", "tests/test_retrieval.py::TestChunkProcessor::test_processor_initialization", "tests/test_retrieval.py::TestChunkProcessor::test_sort_chunks", "tests/test_retrieval.py::TestChunkProcessor::test_sort_chunks_by_similarity", "tests/test_retrieval.py::TestSupabaseRetriever::test_get_available_files", "tests/test_retrieval.py::TestSupabaseRetriever::test_get_file_pages_ordered", "tests/test_retrieval.py::TestSupabaseRetriever::test_health_check", "tests/test_retrieval.py::TestSupabaseRetriever::test_health_check_failure", "tests/test_retrieval.py::TestSupabaseRetriever::test_retriever_initialization", "tests/test_retrieval.py::TestSupabaseRetriever::test_retriever_initialization_missing_credentials", "tests/test_retrieval.py::TestSupabaseRetriever::test_search_multiple_queries", "tests/test_retrieval.py::TestSupabaseRetriever::test_semantic_search", "tests/test_ui.py::TestExtractionApp::test_app_initialization", "tests/test_ui.py::TestExtractionApp::test_export_csv", "tests/test_ui.py::TestExtractionApp::test_export_json", "tests/test_ui.py::TestExtractionApp::test_export_no_data", "tests/test_ui.py::TestExtractionApp::test_extract_data", "tests/test_ui.py::TestExtractionApp::test_format_chunks_for_display", "tests/test_ui.py::TestExtractionApp::test_format_results_for_display", "tests/test_ui.py::TestExtractionApp::test_get_available_files", "tests/test_ui.py::TestExtractionApp::test_get_available_files_error", "tests/test_ui.py::TestExtractionApp::test_get_available_models", "tests/test_ui.py::TestExtractionApp::test_get_system_status", "tests/test_ui.py::TestExtractionApp::test_retrieve_chunks"]