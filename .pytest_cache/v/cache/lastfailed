{"tests/test_extraction.py::TestLLMExtractor::test_extract_success": true, "tests/test_extraction.py::TestLLMExtractor::test_extract_with_fallback": true, "tests/test_extraction.py::TestExtractionModels::test_model_validation": true, "tests/test_retrieval.py::TestSupabaseRetriever::test_retriever_initialization_missing_credentials": true, "tests/test_retrieval.py::TestChunkProcessor::test_filter_chunks_by_file_names": true, "tests/test_retrieval.py::TestChunkProcessor::test_filter_chunks_by_page_range": true, "tests/test_retrieval.py::TestChunkProcessor::test_filter_chunks_by_similarity": true, "tests/test_retrieval.py::TestChunkProcessor::test_filter_chunks_by_keywords": true, "tests/test_ui.py::TestExtractionApp": true, "tests/test_ui.py::TestExtractionApp::test_app_initialization": true, "tests/test_ui.py::TestExtractionApp::test_get_available_files": true, "tests/test_ui.py::TestExtractionApp::test_get_available_files_error": true, "tests/test_ui.py::TestExtractionApp::test_get_available_models": true, "tests/test_ui.py::TestExtractionApp::test_retrieve_chunks": true, "tests/test_ui.py::TestExtractionApp::test_extract_data": true, "tests/test_ui.py::TestExtractionApp::test_export_json": true, "tests/test_ui.py::TestExtractionApp::test_export_csv": true, "tests/test_ui.py::TestExtractionApp::test_export_no_data": true, "tests/test_ui.py::TestExtractionApp::test_format_chunks_for_display": true, "tests/test_ui.py::TestExtractionApp::test_format_results_for_display": true, "tests/test_ui.py::TestExtractionApp::test_get_system_status": true}