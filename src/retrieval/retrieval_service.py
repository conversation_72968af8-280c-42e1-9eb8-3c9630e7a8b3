"""
Shared retrieval service for both CLI and UI.
Centralizes chunk retrieval logic to avoid duplication.
"""

import logging
from typing import List, Optional
from ..config.config_manager import ExtractionConfiguration
from .supabase_retriever import SupabaseRetriever
from .chunk_processor import ChunkProcessor
from ..core.base_classes import ChunkData

logger = logging.getLogger(__name__)


class RetrievalService:
    """Shared service for retrieving and processing chunks."""
    
    def __init__(self):
        """Initialize the retrieval service."""
        self.retriever = SupabaseRetriever()
        self.processor = ChunkProcessor()
    
    def retrieve_chunks(self,
                       file_name: str,
                       config: ExtractionConfiguration,
                       max_chunks: int = 20,
                       use_search_queries: bool = True) -> List[ChunkData]:
        """
        Retrieve and process chunks for extraction.
        
        Args:
            file_name: Name of the file to process
            config: Extraction configuration
            max_chunks: Maximum total chunks to return after deduplication
            use_search_queries: Whether to use search queries or get all pages
            
        Returns:
            List of processed chunks ready for extraction
        """
        logger.info(f"Retrieving chunks from {file_name}...")
        
        # Retrieve raw chunks
        if use_search_queries and config.search_queries:
            chunks = self._retrieve_with_search_queries(
                file_name, config, max_chunks
            )
        else:
            chunks = self._retrieve_all_pages(file_name, max_chunks)
        
        if not chunks:
            logger.warning(f"No chunks found for file: {file_name}")
            return []
        
        # Process and deduplicate chunks
        processed_chunks = self.processor.deduplicate_chunks(chunks)
        
        # Apply max_chunks limit after deduplication if needed
        if len(processed_chunks) > max_chunks:
            logger.info(f"Limiting from {len(processed_chunks)} to {max_chunks} chunks")
            processed_chunks = processed_chunks[:max_chunks]
        
        # Log page numbers of final chunks
        page_numbers = [chunk.page_number for chunk in processed_chunks]
        logger.info(f"Final chunk set: {len(processed_chunks)} chunks from pages: {sorted(set(page_numbers))}")
        
        return processed_chunks
    
    def _retrieve_with_search_queries(self,
                                    file_name: str,
                                    config: ExtractionConfiguration,
                                    max_chunks: int) -> List[ChunkData]:
        """Retrieve chunks using search queries."""
        # Ensure at least 10 chunks per query for comprehensive coverage
        min_chunks_per_query = 10
        max_chunks_per_query = max(min_chunks_per_query, max_chunks // len(config.search_queries))
        
        logger.info(f"Using {len(config.search_queries)} search queries with {max_chunks_per_query} chunks per query")
        
        chunks = self.retriever.search_multiple_queries(
            config.search_queries, file_name, max_chunks_per_query
        )
        
        return chunks
    
    def _retrieve_all_pages(self, file_name: str, max_chunks: int) -> List[ChunkData]:
        """Retrieve all pages from the file."""
        logger.info(f"Retrieving all pages from {file_name} (limit: {max_chunks})")
        
        all_chunks = self.retriever.get_file_pages_ordered(file_name)
        chunks = all_chunks[:max_chunks]
        
        page_numbers = [chunk.page_number for chunk in chunks]
        logger.info(f"Retrieved {len(chunks)} chunks from pages: {sorted(set(page_numbers))}")
        
        return chunks
